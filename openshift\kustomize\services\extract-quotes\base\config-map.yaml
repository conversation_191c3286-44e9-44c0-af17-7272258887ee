---
kind: ConfigMap
apiVersion: v1
metadata:
  name: extract-quotes-service
  namespace: default
  annotations:
    description: Extract Quotes service configuration settings
    created-by: kyle.morris
  labels:
    name: extract-quotes-service
    part-of: tno
    version: 1.0.0
    component: extract-quotes-service
    managed-by: kustomize
data:
  KAFKA_CLIENT_ID: ExtractQuotes
  MAX_FAIL_LIMIT: "5"
  TOPICS: index
  CHES_EMAIL_ENABLED: "true"
  CHES_EMAIL_AUTHORIZED: "true"
  CORENLP_URL: http://corenlp:9000
  EXTRACT_QUOTES_ON_INDEX: "false"
  EXTRACT_QUOTES_ON_PUBLISH: "true"
  IGNORE_CONTENT_PUBLISHED_BEFORE_OFFSET: "7"
  USE_LLM: "true"
  PRIMARY_MODEL_NAME: gemini-2.0-flash-lite
  PRIMARY_API_URL: https://generativelanguage.googleapis.com/v1beta/openai/chat/completions
  FALLBACK_MODEL_NAME: mistral-large-latest
  FALLBACK_API_URL: https://api.mistral.ai/v1/chat/completions
  MAX_REQUESTS_PER_MINUTE: "30"
  RETRY_LIMIT: "1"
  RETRY_DELAY_MS: "1000"
