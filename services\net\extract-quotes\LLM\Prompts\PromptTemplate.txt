Task: Extract all direct quotes from the provided text.
Identify the speaker for each quote. If the speaker cannot be identified, DO NOT include the quote in the results.

Existing Quotes (DO NOT include these or similar quotes in your response):
----------
{ExistingQuotes}
----------

Input Text:
----------
{InputText}
----------

Output Format: Return the result strictly in the following JSON format. Ensure the entire output is valid JSON according to RFC 8259.
```json
{
  "quotes": [
    {
      "id": 1, // Sequential integer ID for each quote
      "text": "The exact quote text, including surrounding quotation marks if present in the original text.",
      "canonicalSpeaker": "The identified speaker's name",
      "beginSentence": 0 // Placeholder, can be always 0
    }
    // ... more quote objects
  ]
}
```

CRITICAL JSON FORMATTING RULES (Strict adherence required):
1.  The entire output MUST be a single, valid JSON object.
2.  JSON Strings (values for "text" and "canonicalSpeaker"):
    *   ONLY escape double quotes (") as \" and backslashes (\) as \\.
    *   DO NOT escape single quotes ('). For example, 'it's' must appear as "it's" in the JSON string, NOT "it\'s". The sequence \' is INVALID in standard JSON strings.
    *   Preserve all original punctuation and capitalization within the quote text.
3.  Do not include trailing commas in JSON arrays or objects.
4.  Use only double quotes (") for all JSON keys and string values.
5.  If no quotes are found in the text, return an empty array like this: { "quotes": [] }.
6.  Ensure if you find a speaker has full name, always keep using the speaker's full name.
7.  If you found a speaker's quote is similar to an existing quote, do not include it in the response.
8.  If you found a quote but cannot identify the speaker, DO NOT include it in the response.
9.  Be consistent with ending punctuation - don't add or remove periods, commas, etc. at the end of quotes.
10. Consider quotes that differ only in trailing punctuation as the same quote and choose the most complete version.
11. DO NOT include redundant attribution at the end of quotes. For example, if the quote is "We are never getting yesterday back, not as an industry and not as a city," said Mr. Zayadi.", do not include "— Greg Zayadi" as a separate quote.
12. For texts identified as a 'Letter to the Editor' (or similar formats where an author signs off on their own written text, such as opinion pieces):
    a. The letter writer's own direct statements, opinions, and narrative within the body of the letter (e.g., sentences like 'I’m getting scared.' written by the letter's author) are NOT to be extracted as quotes for the purpose of this task, even if these statements are immediately followed by the author's name as a signature. The task is to extract material that is being quoted, not the primary assertions or entire content of the letter writer themselves.
    b. The letter writer's name appearing as a signature or sign-off at the end of the letter (e.g., 'Richard Harris' at the end of his letter) MUST NOT be treated as a quote itself. Furthermore, the signature should not cause the immediately preceding sentence of the letter writer's own text to be treated as an extractable quote attributed to them.
    c. If the entire content is identified as a letter to the editor and it contains no explicit quotations of *other* parties made by the letter writer (as per rule 12b), then the output should be { "quotes": [] }, regardless of the declarative statements made by the letter writer in the body of the letter.