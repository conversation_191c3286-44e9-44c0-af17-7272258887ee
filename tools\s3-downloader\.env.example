# S3 Configuration, please ask developers for the URL and bucket names
S3_SERVICE_URL=bucket_url
S3_BUCKET_NAME=local
# leave keys empty,let user input.
S3_ACCESS_KEY=
S3_SECRET_KEY=
S3_TIMEOUT=2

# Local Storage Configuration
LOCAL_STORAGE_PATH=./downloads

# Database Configuration
DATABASE_URL=sqlite:///./s3_downloader.db

# Scheduler Configuration
SCHEDULER_INTERVAL=3600  # Download interval in seconds (default: 3600 = 1 hour)

# Network Error Handling Configuration
MAX_CONSECUTIVE_FAILURES=3  # Maximum consecutive failures before aborting download task
MAX_FAILURE_PERCENTAGE=0.3  # Maximum percentage of failures before aborting (0.0-1.0)
NETWORK_TEST_INTERVAL=5     # Number of consecutive failures before testing network connection

# Disk Space Configuration
SPACE_WARNING_THRESHOLD=0.1  # Minimum free disk space ratio before warning (0.0-1.0)
