---
apiVersion: kustomize.config.k8s.io/v1beta1
kind: Kustomization
namespace: 9b301c-test

resources:
  - ../../base

generatorOptions:
  disableNameSuffixHash: true

secretGenerator:
  - name: llm-api-keys
    type: Opaque
    env: llm-api-keys.env

patches:
  - target:
      kind: DeploymentConfig
      name: extract-quotes-service
    patch: |-
      - op: replace
        path: /spec/replicas
        value: 1
      - op: replace
        path: /spec/template/spec/containers/0/resources/requests/cpu
        value: 20m
      - op: replace
        path: /spec/template/spec/containers/0/resources/requests/memory
        value: 80Mi
      - op: replace
        path: /spec/template/spec/containers/0/resources/limits/cpu
        value: 100m
      - op: replace
        path: /spec/template/spec/containers/0/resources/limits/memory
        value: 150Mi
      - op: replace
        path: /spec/triggers/1/imageChangeParams/from/name
        value: extract-quotes-service:test
  - target:
      kind: ConfigMap
      name: extract-quotes-service
    patch: |-
      - op: replace
        path: /data/CHES_EMAIL_ENABLED
        value: "true"
      - op: replace
        path: /data/CHES_EMAIL_AUTHORIZED
        value: "false"
