{"name": "tno-core", "description": "TNO shared library", "version": "1.0.13", "homepage": "https://github.com/bcgov/tno", "license": "Apache-2.0", "files": ["dist/**/*", "src/**/*", "package.json"], "main": "dist/index.js", "module": "dist/index.js", "unpkg": "dist/index.min.js", "types": "dist/index.d.ts", "engines": {"npm": ">=8.19.2 <9.0.0", "node": ">=18.11.0 <19.0.0", "yarn": ">=3.2.0 <4.0.0"}, "dependencies": {"@elastic/elasticsearch": "^8.13.1", "@fortawesome/free-solid-svg-icons": "^6.4.2", "@fortawesome/react-fontawesome": "^0.2.0", "@headlessui/react": "^2.0.4", "@react-keycloak/web": "^3.4.0", "axios": "^1.7.2", "dequal": "^2.0.3", "formik": "^2.4.6", "js-beautify": "^1.14.11", "keycloak-js": "^24.0.4", "lodash": "^4.17.21", "lodash.throttle": "^4.1.1", "moment": "^2.29.4", "moment-timezone": "^0.5.41", "rc-slider": "^10.2.1", "react": "^18.2.0", "react-beautiful-dnd": "^13.1.1", "react-datepicker": "^6.9.0", "react-dom": "^18.2.0", "react-icons": "^5.2.1", "react-inlinesvg": "^4.1.3", "react-quill-new": "^3.3.1", "react-router-dom": "^6.9.0", "react-select": "^5.8.0", "react-table": "^7.8.0", "react-text-mask": "^5.5.0", "react-toastify": "^10.0.5", "react-tooltip": "^5.10.0", "styled-components": "^6.1.11", "stylis": "^4.3.2", "yup": "^1.1.1"}, "devDependencies": {"@testing-library/jest-dom": "^6.4.5", "@testing-library/react": "^15.0.7", "@testing-library/user-event": "^14.4.3", "@types/customize-cra": "^1.0.4", "@types/jest": "^29.4.2", "@types/js-beautify": "^1.14.3", "@types/lodash.throttle": "^4.1.7", "@types/node": "^20.12.12", "@types/pretty": "^2.0.1", "@types/react": "^18.0.28", "@types/react-beautiful-dnd": "^13.1.5", "@types/react-datepicker": "^6.2.0", "@types/react-dom": "^18.0.11", "@types/react-router-dom": "^5.3.3", "@types/react-table": "^7.7.14", "@types/react-text-mask": "^5.4.11", "@types/styled-components": "^5.1.26", "@typescript-eslint/eslint-plugin": "^7.10.0", "@typescript-eslint/parser": "^7.10.0", "axios-mock-adapter": "^1.21.5", "compression-webpack-plugin": "^11.1.0", "cross-env": "^7.0.3", "customize-cra": "^1.0.0", "eslint": "8.36.0", "eslint-config-prettier": "9.1.0", "eslint-config-react-app": "^7.0.1", "eslint-plugin-flowtype": "^8.0.3", "eslint-plugin-prettier": "4.2.1", "eslint-plugin-react-hooks": "4.6.2", "eslint-plugin-simple-import-sort": "12.1.0", "node-sass": "9.0.0", "prettier": "^2.8.4", "pretty": "^2.0.0", "pretty-quick": "^3.1.3", "sass-extract": "3.0.0", "sass-extract-js": "^0.4.0", "sass-extract-loader": "^1.1.0", "ts-loader": "^9.4.2", "typescript": "4.9.5"}, "scripts": {"prebuild": "rm -rf dist/", "build": "tsc --build", "prepack": "yarn run copy-files", "copy-files": "cp -R ./src/assets ./dist; cp -R ./src/@types ./dist; cp -R ./src/css ./dist;", "clean": "rm -rf dist/; find ./src -type f -name '*.d.ts' ! -path './src/@types/*' -delete;", "pretty-quick": "pretty-quick", "lint": "eslint src/ --ext .jsx,.js,.ts,.tsx --max-warnings 0", "lint:fix": "npm run lint -- --fix", "format": "prettier --write \"./src/**/*.{js,jsx,ts,tsx,json,css,scss}\"", "check": "prettier --check \"./src/**/*.{js,jsx,ts,tsx,css,scss}\"", "prepublish": "yarn build"}, "eslintConfig": {"extends": ["react-app", "plugin:prettier/recommended"], "plugins": ["simple-import-sort"], "rules": {"simple-import-sort/imports": "error", "simple-import-sort/exports": "error"}}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "packageManager": "yarn@3.2.0"}