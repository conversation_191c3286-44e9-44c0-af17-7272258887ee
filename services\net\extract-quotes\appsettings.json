{"BaseUrl": "/", "Logging": {"Console": {"DisableColors": true}, "LogLevel": {"Default": "Warning", "Microsoft": "Error", "TNO": "Information"}}, "AllowedHosts": "*", "Service": {"MaxFailLimit": 5, "ApiUrl": "http://api:8080", "TimeZone": "Pacific Standard Time", "Topics": "index", "SendEmailOnFailure": true, "UseLLM": true, "QuoteExtractionPromptTemplate": "Task: Extract all direct quotes from the provided text.\nIdentify the speaker for each quote. If the speaker cannot be identified, DO NOT include the quote in the results.\n\nExisting Quotes (DO NOT include these or similar quotes in your response):\n----------\n{ExistingQuotes}\n----------\n\nInput Text:\n----------\n{InputText}\n----------\n\nOutput Format: Return the result strictly in the following JSON format. Ensure the entire output is valid JSON according to RFC 8259.\n```json\n{\n  \"quotes\": [\n    {\n      \"id\": 1, // Sequential integer ID for each quote\n      \"text\": \"The exact quote text, including surrounding quotation marks if present in the original text.\",\n      \"canonicalSpeaker\": \"The identified speaker's name\",\n      \"beginSentence\": 0 // Placeholder, can be always 0\n    }\n    // ... more quote objects\n  ]\n}\n```\n\nCRITICAL JSON FORMATTING RULES (Strict adherence required):\n1.  The entire output MUST be a single, valid JSON object.\n2.  JSON Strings (values for \"text\" and \"canonicalSpeaker\"):\n    *   ONLY escape double quotes (\") as \\\" and backslashes (\\) as \\\\.\n    *   DO NOT escape single quotes ('). For example, 'it's' must appear as \"it's\" in the JSON string, NOT \"it\\'s\". The sequence \\' is INVALID in standard JSON strings.\n    *   Preserve all original punctuation and capitalization within the quote text.\n3.  Do not include trailing commas in JSON arrays or objects.\n4.  Use only double quotes (\") for all JSON keys and string values.\n5.  If no quotes are found in the text, return an empty array like this: { \"quotes\": [] }.\n6.  Ensure if you find a speaker has full name, always keep using the speaker's full name.\n7.  If you found a speaker's quote is similar to an existing quote, do not include it in the response.\n8.  If you found a quote but cannot identify the speaker, DO NOT include it in the response.\n9.  Be consistent with ending punctuation - don't add or remove periods, commas, etc. at the end of quotes.\n10. Consider quotes that differ only in trailing punctuation as the same quote and choose the most complete version.\n11. DO NOT include redundant attribution at the end of quotes. For example, if the quote is \"We are never getting yesterday back, not as an industry and not as a city,\" said Mr. Zayadi.\", do not include \"— Greg Zayadi\" as a separate quote.\n12. For texts identified as a 'Letter to the Editor' (or similar formats where an author signs off on their own written text, such as opinion pieces):\n    a. The letter writer's own direct statements, opinions, and narrative within the body of the letter (e.g., sentences like 'I’m getting scared.' written by the letter's author) are NOT to be extracted as quotes for the purpose of this task, even if these statements are immediately followed by the author's name as a signature. The task is to extract material that is being quoted, not the primary assertions or entire content of the letter writer themselves.\n    b. The letter writer's name appearing as a signature or sign-off at the end of the letter (e.g., 'Richard Harris' at the end of his letter) MUST NOT be treated as a quote itself. Furthermore, the signature should not cause the immediately preceding sentence of the letter writer's own text to be treated as an extractable quote attributed to them.\n    c. If the entire content is identified as a letter to the editor and it contains no explicit quotations of *other* parties made by the letter writer (as per rule 12b), then the output should be { \"quotes\": [] }, regardless of the declarative statements made by the letter writer in the body of the letter.", "PrimaryApiKeys": "PLACEHOLDER_PRIMARY_KEY_1;PLACEHOLDER_PRIMARY_KEY_2", "PrimaryModelName": "PLACEHOLDER_PRIMARY_MODEL", "PrimaryApiUrl": "PLACEHOLDER_PRIMARY_URL", "FallbackApiKeys": "PLACEHOLDER_FALLBACK_KEY_1;PLACEHOLDER_FALLBACK_KEY_2", "FallbackModelName": "PLACEHOLDER_FALLBACK_MODEL", "FallbackApiUrl": "PLACEHOLDER_FALLBACK_URL", "MaxRequestsPerMinute": 10}, "CHES": {"AuthUrl": "https://loginproxy.gov.bc.ca/auth/realms/comsvcauth/protocol/openid-connect/token", "HostUri": "https://ches.api.gov.bc.ca/api/v1", "From": "Media Monitoring Insights <<EMAIL>>", "EmailEnabled": true, "EmailAuthorized": false}, "Auth": {"Keycloak": {"Authority": "https://loginproxy.gov.bc.ca/auth", "Audience": "mmi-service-account", "Secret": "{DO NOT STORE SECRET HERE}"}, "OIDC": {"Token": "/realms/mmi/protocol/openid-connect/token"}}, "Serialization": {"Json": {"PropertyNamingPolicy": "CamelCase", "PropertyNameCaseInsensitive": true, "DefaultIgnoreCondition": "WhenWritingNull", "WriteIndented": true}}, "Kafka": {"Consumer": {"GroupId": "ExtractQuotes", "BootstrapServers": "kafka-broker-0.kafka-headless:9092,kafka-broker-1.kafka-headless:9092,kafka-broker-2.kafka-headless:9092", "AutoOffsetReset": "Latest", "MaxInFlight": 5, "EnableAutoCommit": false, "MaxThreads": 1}}}